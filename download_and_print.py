#!/usr/bin/env python3
"""
网页和PDF下载打印程序
自动从CSV文件读取URL，下载内容并转换为PDF进行打印
"""

import csv
import os
import sys
import time
import requests
from urllib.parse import urlparse, urljoin
from pathlib import Path
import logging
from typing import List, Tuple, Optional

# 导入所需的库
try:
    import pdfkit
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    import PyPDF2
except ImportError as e:
    print(f"缺少必要的库: {e}")
    print("请安装所需依赖:")
    print("pip install requests pdfkit selenium PyPDF2")
    print("pip install webdriver-manager")
    print("还需要安装 wkhtmltopdf: https://wkhtmltopdf.org/downloads.html")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('download_log.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WebPageDownloader:
    def __init__(self, csv_file: str, output_dir: str = "downloaded_pages"):
        self.csv_file = csv_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        self.pdf_dir = self.output_dir / "pdfs"
        self.html_dir = self.output_dir / "html_pages"
        self.pdf_dir.mkdir(exist_ok=True)
        self.html_dir.mkdir(exist_ok=True)
        
        # 配置请求会话
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # 配置Chrome选项
        self.chrome_options = Options()
        self.chrome_options.add_argument('--headless')
        self.chrome_options.add_argument('--no-sandbox')
        self.chrome_options.add_argument('--disable-dev-shm-usage')
        self.chrome_options.add_argument('--disable-gpu')
        self.chrome_options.add_argument('--window-size=1920,1080')
        
        # PDF生成选项
        self.pdf_options = {
            'page-size': 'A4',
            'margin-top': '0.75in',
            'margin-right': '0.75in',
            'margin-bottom': '0.75in',
            'margin-left': '0.75in',
            'encoding': "UTF-8",
            'no-outline': None,
            'enable-local-file-access': None
        }

    def read_urls_from_csv(self) -> List[Tuple[str, str, str, str]]:
        """从CSV文件读取URL列表"""
        urls = []
        try:
            with open(self.csv_file, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    if row.get('URL') and row['URL'].strip():
                        urls.append((
                            row.get('Topic', '').strip(),
                            row.get('Date', '').strip(),
                            row.get('Summary', '').strip(),
                            row['URL'].strip()
                        ))
        except Exception as e:
            logger.error(f"读取CSV文件失败: {e}")
            return []
        
        logger.info(f"从CSV文件读取到 {len(urls)} 个URL")
        return urls

    def is_pdf_url(self, url: str) -> bool:
        """检查URL是否指向PDF文件"""
        try:
            response = self.session.head(url, timeout=10, allow_redirects=True)
            content_type = response.headers.get('content-type', '').lower()
            return 'application/pdf' in content_type or url.lower().endswith('.pdf')
        except:
            return url.lower().endswith('.pdf')

    def download_pdf(self, url: str, filename: str) -> bool:
        """下载PDF文件"""
        try:
            logger.info(f"下载PDF: {url}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            pdf_path = self.pdf_dir / f"{filename}.pdf"
            with open(pdf_path, 'wb') as f:
                f.write(response.content)
            
            logger.info(f"PDF保存到: {pdf_path}")
            return True
        except Exception as e:
            logger.error(f"下载PDF失败 {url}: {e}")
            return False

    def convert_webpage_to_pdf(self, url: str, filename: str) -> bool:
        """将网页转换为PDF"""
        try:
            logger.info(f"转换网页为PDF: {url}")

            # 方法1: 尝试使用pdfkit (如果可用)
            try:
                import shutil
                if shutil.which('wkhtmltopdf'):
                    pdf_path = self.html_dir / f"{filename}.pdf"
                    pdfkit.from_url(url, str(pdf_path), options=self.pdf_options)
                    logger.info(f"网页PDF保存到: {pdf_path}")
                    return True
                else:
                    logger.info("wkhtmltopdf未找到，使用Selenium方法")
                    raise Exception("wkhtmltopdf not found")
            except Exception as e1:
                logger.warning(f"pdfkit转换失败，尝试Selenium: {e1}")

                # 方法2: 使用Selenium + Chrome打印功能
                return self.selenium_to_pdf(url, filename)

        except Exception as e:
            logger.error(f"网页转换PDF失败 {url}: {e}")
            return False

    def selenium_to_pdf(self, url: str, filename: str) -> bool:
        """使用Selenium获取网页内容并转换为PDF"""
        driver = None
        try:
            from selenium.webdriver.chrome.service import Service
            from webdriver_manager.chrome import ChromeDriverManager

            # 配置Chrome选项以支持打印为PDF
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--run-all-compositor-stages-before-draw')
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-renderer-backgrounding')
            chrome_options.add_argument('--disable-features=TranslateUI')
            chrome_options.add_argument('--disable-ipc-flooding-protection')

            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)

            driver.get(url)
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # 等待页面完全加载
            time.sleep(5)

            # 方法1: 尝试使用Chrome的打印功能
            try:
                pdf_path = self.html_dir / f"{filename}.pdf"

                # 使用Chrome DevTools Protocol打印为PDF
                result = driver.execute_cdp_cmd('Page.printToPDF', {
                    'format': 'A4',
                    'printBackground': True,
                    'marginTop': 0.4,
                    'marginBottom': 0.4,
                    'marginLeft': 0.4,
                    'marginRight': 0.4,
                    'scale': 0.8
                })

                import base64
                with open(pdf_path, 'wb') as f:
                    f.write(base64.b64decode(result['data']))

                logger.info(f"Chrome打印PDF保存到: {pdf_path}")
                return True

            except Exception as e2:
                logger.warning(f"Chrome打印失败，保存为HTML: {e2}")

                # 方法2: 保存为HTML文件
                html_content = driver.page_source
                html_path = self.html_dir / f"{filename}.html"
                with open(html_path, 'w', encoding='utf-8') as f:
                    f.write(f"<!-- URL: {url} -->\n")
                    f.write(f"<!-- 下载时间: {time.strftime('%Y-%m-%d %H:%M:%S')} -->\n")
                    f.write(html_content)

                logger.info(f"网页HTML保存到: {html_path}")
                return True

        except Exception as e:
            logger.error(f"Selenium处理失败: {e}")
            return False
        finally:
            if driver:
                driver.quit()

    def sanitize_filename(self, text: str, max_length: int = 100) -> str:
        """清理文件名，移除非法字符"""
        import re
        # 移除非法字符
        text = re.sub(r'[<>:"/\\|?*]', '_', text)
        # 限制长度
        if len(text) > max_length:
            text = text[:max_length]
        return text.strip()

    def print_file(self, file_path: Path) -> bool:
        """打印文件（PDF或HTML）"""
        try:
            import subprocess
            import platform

            system = platform.system()
            if system == "Windows":
                # Windows系统使用默认程序打开文件
                os.startfile(str(file_path))
                logger.info(f"已打开文件进行打印: {file_path}")
            elif system == "Darwin":  # macOS
                if file_path.suffix.lower() == '.pdf':
                    subprocess.run(['lpr', str(file_path)], check=True)
                else:
                    subprocess.run(['open', str(file_path)], check=True)
                logger.info(f"已发送打印任务: {file_path}")
            else:  # Linux
                if file_path.suffix.lower() == '.pdf':
                    subprocess.run(['lp', str(file_path)], check=True)
                else:
                    subprocess.run(['xdg-open', str(file_path)], check=True)
                logger.info(f"已发送打印任务: {file_path}")

            return True
        except Exception as e:
            logger.error(f"打印失败 {file_path}: {e}")
            return False

    def process_url(self, topic: str, date: str, summary: str, url: str, index: int) -> bool:
        """处理单个URL"""
        # 生成文件名
        filename_parts = [
            f"{index:03d}",
            self.sanitize_filename(topic, 30),
            self.sanitize_filename(date, 15),
        ]
        filename = "_".join(filter(None, filename_parts))
        
        logger.info(f"处理 [{index}] {topic} - {url}")
        
        success = False
        pdf_path = None
        
        if self.is_pdf_url(url):
            # 直接下载PDF
            if self.download_pdf(url, filename):
                pdf_path = self.pdf_dir / f"{filename}.pdf"
                success = True
        else:
            # 转换网页为PDF
            if self.convert_webpage_to_pdf(url, filename):
                pdf_path = self.html_dir / f"{filename}.pdf"
                success = True
        
        # 打印文件
        if success:
            # 查找生成的文件
            possible_files = [
                self.pdf_dir / f"{filename}.pdf",
                self.html_dir / f"{filename}.pdf",
                self.html_dir / f"{filename}.html"
            ]

            for file_path in possible_files:
                if file_path.exists():
                    self.print_file(file_path)
                    return True
        
        return False

    def run(self, print_files: bool = True, delay: float = 2.0):
        """运行下载和打印程序"""
        urls = self.read_urls_from_csv()
        if not urls:
            logger.error("没有找到有效的URL")
            return
        
        logger.info(f"开始处理 {len(urls)} 个URL")
        
        success_count = 0
        failed_urls = []
        
        for i, (topic, date, summary, url) in enumerate(urls, 1):
            try:
                if self.process_url(topic, date, summary, url, i):
                    success_count += 1
                else:
                    failed_urls.append((i, url))
                
                # 添加延迟避免过于频繁的请求
                if i < len(urls):
                    time.sleep(delay)
                    
            except KeyboardInterrupt:
                logger.info("用户中断程序")
                break
            except Exception as e:
                logger.error(f"处理URL失败 [{i}] {url}: {e}")
                failed_urls.append((i, url))
        
        # 输出统计信息
        logger.info(f"处理完成! 成功: {success_count}/{len(urls)}")
        if failed_urls:
            logger.warning("失败的URL:")
            for i, url in failed_urls:
                logger.warning(f"  [{i}] {url}")

def main():
    """主函数"""
    csv_file = "需要下载_最终优化版.csv"
    
    if not os.path.exists(csv_file):
        print(f"错误: 找不到CSV文件 '{csv_file}'")
        print("请确保CSV文件在当前目录中")
        return
    
    print("网页和PDF下载打印程序")
    print("=" * 50)
    print(f"CSV文件: {csv_file}")
    
    # 询问用户是否要打印
    print_choice = input("是否要自动打印下载的PDF? (y/n, 默认y): ").strip().lower()
    print_files = print_choice != 'n'
    
    # 询问延迟时间
    delay_input = input("请求间隔延迟秒数 (默认2秒): ").strip()
    try:
        delay = float(delay_input) if delay_input else 2.0
    except ValueError:
        delay = 2.0
    
    downloader = WebPageDownloader(csv_file)
    downloader.run(print_files=print_files, delay=delay)

if __name__ == "__main__":
    main()
